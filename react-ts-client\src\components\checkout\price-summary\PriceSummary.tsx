import React from "react"
import { RiQuestionFill } from "react-icons/ri"
import Tooltip from '../../utils/tooltip/Tooltip'
import styles from './PriceSummary.module.scss'

interface PriceSummaryProps {
  totalPrice: number
  // shippingCost: number
  // grandTotal: number
  item_count: number
  cart_weight: number
  onCheckout?: () => void
}

const PriceSummary: React.FC<PriceSummaryProps> = ({ totalPrice, item_count, cart_weight, onCheckout }) => {
  return (
    <div className={styles.cart__checkout}>
      <div>
        <p>Item's count: </p> <p>{item_count}</p>
      </div>
      <div>
        <p>Cart weight:
          <Tooltip
            content={`Weight of cart items)`}
            position="top"
          >
            <i><RiQuestionFill /></i>
          </Tooltip>
        </p>
        <p>{cart_weight}g</p>
      </div>
      <div>
        <p>Total: </p> <p>${totalPrice.toFixed(2)}</p>
      </div>
      {onCheckout && <button onClick={onCheckout}>Proceed to checkout</button>}
    </div>
  )
}

export default PriceSummary
